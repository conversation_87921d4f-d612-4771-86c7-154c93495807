import { TestBed } from "@angular/core/testing";
import { UserManagementService } from "./user-management.service";

describe("UserManagementService", () => {
  let service: UserManagementService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [UserManagementService]
    });

    service = TestBed.inject(UserManagementService);
  });

  it("should be created", () => {
    expect(service).toBeTruthy();
  });

  it("should have userProfile$ observable", () => {
    expect(service.userProfile$).toBeDefined();
  });

  describe("getUserProfile", () => {
    it("should return mock user profile", (done) => {
      service.getUserProfile().subscribe(profile => {
        expect(profile).toBeDefined();
        expect(profile.firstName).toBeDefined();
        expect(profile.lastName).toBeDefined();
        expect(profile.email).toBeDefined();
        done();
      });
    });
  });
});
